{"name": "hydronepal-interactive", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@types/d3": "^7.4.3", "@types/leaflet": "^1.9.20", "d3": "^7.9.0", "framer-motion": "^12.23.15", "leaflet": "^1.9.4", "next": "15.5.3", "react": "19.1.0", "react-dom": "19.1.0", "react-leaflet": "^5.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.3", "tailwindcss": "^4", "typescript": "^5"}}