# HydroNepal Interactive Guide

An interactive, educational website that explores Nepal's hydropower potential and the fundamentals of hydroelectric power generation. This single-page application provides an engaging journey through the world of hydropower, specifically focused on Nepal's unique geographical advantages and energy landscape.

## 🌟 Features

### Interactive Components
- **Animated Hydropower Diagram**: Step-by-step visualization of how hydropower plants generate electricity
- **Interactive Map**: Explore Nepal's major hydropower projects with detailed information
- **Data Visualizations**: D3.js-powered charts showing Nepal's hydropower potential vs. current capacity
- **Glossary Cards**: Flip cards explaining key hydropower terminology
- **Responsive Design**: Optimized for both desktop and mobile devices

### Educational Content
- **How Hydropower Works**: Clear explanation of the electricity generation process
- **Nepal Context**: Understanding why Nepal is ideal for hydropower development
- **Balanced Analysis**: Comprehensive view of benefits and challenges
- **Key Statistics**: Current capacity, potential, and development status

## 🚀 Tech Stack

- **Frontend**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS for modern, responsive design
- **Animations**: Framer Motion for smooth interactions
- **Data Visualization**: D3.js for interactive charts
- **Maps**: Leaflet.js and react-leaflet for interactive mapping
- **Icons**: Heroicons for consistent iconography

## 📋 Prerequisites

- Node.js 18.x or higher
- npm or yarn package manager

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd hydronepal-interactive
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🏗️ Project Structure

```
src/
├── app/
│   ├── globals.css          # Global styles and Tailwind imports
│   ├── layout.tsx           # Root layout with metadata
│   └── page.tsx             # Main page component
├── components/
│   ├── Hero.tsx             # Landing section with animated background
│   ├── HowItWorks.tsx       # Interactive hydropower diagram
│   ├── NepalContext.tsx     # Map and data visualization
│   ├── NepalMap.tsx         # Leaflet map component
│   ├── Glossary.tsx         # Interactive terminology cards
│   ├── ProsCons.tsx         # Benefits and challenges section
│   └── Navigation.tsx       # Responsive navigation bar
```

## 🎯 Target Audience

- **Students** learning about renewable energy
- **Journalists** covering energy and development stories
- **Policymakers** interested in Nepal's energy sector
- **General Public** curious about hydropower and sustainable development

## 🌐 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to [Vercel](https://vercel.com)
3. Deploy automatically with every push

### Netlify
1. Build the project: `npm run build`
2. Deploy the `out` folder to [Netlify](https://netlify.com)

### Other Platforms
The project can be deployed to any static hosting service that supports Next.js.

## 🔧 Customization

### Adding New Hydropower Projects
Edit the `hydropowerProjects` array in `src/components/NepalMap.tsx`:

```typescript
{
  name: "Project Name",
  capacity: "XXX MW",
  status: "Operational", // or "Under Construction", "Planned"
  river: "River Name",
  position: [latitude, longitude],
  icon: operationalIcon // or underConstructionIcon, plannedIcon
}
```

### Updating Statistics
Modify the data object in `src/components/NepalContext.tsx`:

```typescript
const data: HydropowerData = {
  theoretical: 83000,    // MW
  feasible: 42000,       // MW
  installed: 2500        // MW
};
```

### Styling
The project uses Tailwind CSS. Customize colors, spacing, and other design elements by:
- Editing `tailwind.config.js` for global theme changes
- Modifying component classes for specific styling
- Updating `src/app/globals.css` for custom CSS

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes and commit: `git commit -m 'Add feature'`
4. Push to your fork: `git push origin feature-name`
5. Create a Pull Request

## 📊 Performance

- **Lighthouse Score**: Optimized for performance, accessibility, and SEO
- **Bundle Size**: Efficient code splitting with Next.js
- **Loading Speed**: Optimized images and lazy loading
- **Mobile Performance**: Responsive design with touch-friendly interactions

## 🐛 Known Issues

- Map tiles may load slowly on first visit
- Some animations may be reduced on low-power devices
- Internet connection required for map functionality

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🙏 Acknowledgments

- **Next.js Team** for the excellent framework
- **Tailwind CSS** for the utility-first CSS framework
- **D3.js Community** for powerful data visualization tools
- **Leaflet** for open-source mapping capabilities
- **Nepal Department of Electricity Development** for hydropower data

## 📞 Support

For questions, suggestions, or issues:
- Create an issue on GitHub
- Contact the development team
- Check the documentation

---

**Built with ❤️ for Nepal's sustainable energy future**
