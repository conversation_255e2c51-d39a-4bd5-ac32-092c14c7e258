'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';

interface GlossaryTerm {
  term: string;
  definition: string;
  icon: string;
}

export default function Glossary() {
  const [flippedCards, setFlippedCards] = useState<Set<number>>(new Set());

  const glossaryTerms: GlossaryTerm[] = [
    {
      term: "Megawatt (MW)",
      definition: "A unit of power equal to one million watts, used to measure the capacity of power plants.",
      icon: "⚡"
    },
    {
      term: "Run-of-River",
      definition: "A type of hydropower plant that uses the natural flow of a river without a large dam or reservoir.",
      icon: "🌊"
    },
    {
      term: "Reservoir (Storage)",
      definition: "An artificial lake created by building a dam across a river to store water for power generation.",
      icon: "🏞️"
    },
    {
      term: "Turbine",
      definition: "A rotating machine that converts the kinetic energy of flowing water into mechanical energy.",
      icon: "⚙️"
    },
    {
      term: "Penstock",
      definition: "A large pipe that carries water from the reservoir to the turbine in a hydropower plant.",
      icon: "🔧"
    },
    {
      term: "Capacity Factor",
      definition: "The ratio of actual energy output to maximum possible output, measuring plant efficiency.",
      icon: "📊"
    },
    {
      term: "Spillway",
      definition: "A structure that allows controlled release of water from a dam to prevent overflow damage.",
      icon: "🚰"
    },
    {
      term: "Grid Integration",
      definition: "The process of connecting a power plant to the electrical grid for power distribution.",
      icon: "🔌"
    }
  ];

  const toggleCard = (index: number) => {
    const newFlipped = new Set(flippedCards);
    if (newFlipped.has(index)) {
      newFlipped.delete(index);
    } else {
      newFlipped.add(index);
    }
    setFlippedCards(newFlipped);
  };

  return (
    <section className="min-h-screen bg-slate-50 py-20">
      <div className="max-w-7xl mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-light text-slate-800 mb-6">
            Key Terms & Concepts
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            Understanding the language of hydropower - click on any term to learn more
          </p>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {glossaryTerms.map((term, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="relative h-48 cursor-pointer"
              onClick={() => toggleCard(index)}
            >
              <div className="relative w-full h-full preserve-3d transition-transform duration-700"
                   style={{
                     transformStyle: 'preserve-3d',
                     transform: flippedCards.has(index) ? 'rotateY(180deg)' : 'rotateY(0deg)'
                   }}>
                
                {/* Front of card */}
                <div className="absolute inset-0 backface-hidden bg-white rounded-xl shadow-lg border border-slate-200 flex flex-col items-center justify-center p-6 hover:shadow-xl transition-shadow">
                  <div className="text-4xl mb-4">{term.icon}</div>
                  <h3 className="text-lg font-semibold text-slate-800 text-center leading-tight">
                    {term.term}
                  </h3>
                  <div className="mt-4 text-xs text-slate-500 text-center">
                    Click to learn more
                  </div>
                </div>

                {/* Back of card */}
                <div className="absolute inset-0 backface-hidden bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl shadow-lg text-white p-6 flex flex-col justify-center"
                     style={{ transform: 'rotateY(180deg)' }}>
                  <div className="text-2xl mb-3 text-center">{term.icon}</div>
                  <h3 className="text-sm font-bold mb-3 text-center">
                    {term.term}
                  </h3>
                  <p className="text-sm text-blue-100 text-center leading-relaxed">
                    {term.definition}
                  </p>
                  <div className="mt-4 text-xs text-blue-200 text-center">
                    Click to flip back
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="mt-16 bg-white rounded-2xl p-8 shadow-lg"
        >
          <h3 className="text-2xl font-semibold text-slate-800 mb-6 text-center">
            Quick Reference
          </h3>
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h4 className="text-lg font-semibold text-slate-700 mb-4">Power Units</h4>
              <ul className="space-y-2 text-slate-600">
                <li><strong>1 Kilowatt (kW)</strong> = 1,000 watts</li>
                <li><strong>1 Megawatt (MW)</strong> = 1,000 kW</li>
                <li><strong>1 Gigawatt (GW)</strong> = 1,000 MW</li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold text-slate-700 mb-4">Types of Hydropower</h4>
              <ul className="space-y-2 text-slate-600">
                <li><strong>Run-of-River:</strong> Uses natural river flow</li>
                <li><strong>Storage:</strong> Uses dammed reservoir</li>
                <li><strong>Pumped Storage:</strong> Stores energy by pumping water uphill</li>
              </ul>
            </div>
          </div>
        </motion.div>
      </div>

      <style jsx>{`
        .preserve-3d {
          transform-style: preserve-3d;
        }
        .backface-hidden {
          backface-visibility: hidden;
        }
      `}</style>
    </section>
  );
}
