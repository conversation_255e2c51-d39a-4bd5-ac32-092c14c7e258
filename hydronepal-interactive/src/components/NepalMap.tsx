'use client';

// Removed unused useEffect import
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in react-leaflet
// eslint-disable-next-line @typescript-eslint/no-explicit-any
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Custom icons for different types of projects
const createCustomIcon = (color: string) => {
  return L.divIcon({
    className: 'custom-div-icon',
    html: `<div style="background-color:${color}; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>`,
    iconSize: [20, 20],
    iconAnchor: [10, 10],
  });
};

const operationalIcon = createCustomIcon('#10b981');
const underConstructionIcon = createCustomIcon('#f59e0b');
const plannedIcon = createCustomIcon('#6b7280');

// Major hydropower projects in Nepal
const hydropowerProjects = [
  {
    name: "Upper Tamakoshi",
    capacity: "456 MW",
    status: "Operational",
    river: "Tamakoshi River",
    position: [27.8, 86.2] as [number, number],
    icon: operationalIcon
  },
  {
    name: "Chilime",
    capacity: "22.1 MW",
    status: "Operational", 
    river: "Chilime Khola",
    position: [28.2, 85.3] as [number, number],
    icon: operationalIcon
  },
  {
    name: "Kulekhani I",
    capacity: "60 MW",
    status: "Operational",
    river: "Kulekhani River",
    position: [27.6, 85.1] as [number, number],
    icon: operationalIcon
  },
  {
    name: "Upper Karnali",
    capacity: "900 MW",
    status: "Under Construction",
    river: "Karnali River", 
    position: [29.3, 81.2] as [number, number],
    icon: underConstructionIcon
  },
  {
    name: "Budhi Gandaki",
    capacity: "1200 MW",
    status: "Planned",
    river: "Budhi Gandaki River",
    position: [28.5, 84.8] as [number, number],
    icon: plannedIcon
  },
  {
    name: "Arun III",
    capacity: "900 MW",
    status: "Under Construction",
    river: "Arun River",
    position: [27.3, 87.2] as [number, number],
    icon: underConstructionIcon
  },
  {
    name: "West Seti",
    capacity: "750 MW",
    status: "Planned",
    river: "Seti River",
    position: [29.5, 80.8] as [number, number],
    icon: plannedIcon
  }
];

// Major rivers in Nepal (simplified coordinates)
const majorRivers = [
  {
    name: "Koshi River",
    coordinates: [[27.0, 87.5], [27.2, 87.3], [27.4, 87.0], [27.6, 86.8]] as [number, number][]
  },
  {
    name: "Gandaki River", 
    coordinates: [[28.8, 84.0], [28.5, 84.2], [28.2, 84.4], [27.8, 84.6]] as [number, number][]
  },
  {
    name: "Karnali River",
    coordinates: [[29.8, 81.0], [29.5, 81.2], [29.2, 81.4], [28.8, 81.8]] as [number, number][]
  }
];

export default function NepalMap() {
  const center: [number, number] = [28.3949, 84.1240]; // Center of Nepal
  
  return (
    <MapContainer
      center={center}
      zoom={7}
      style={{ height: '100%', width: '100%', borderRadius: '8px' }}
      scrollWheelZoom={false}
    >
      <TileLayer
        attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
      />
      
      {/* Rivers */}
      {majorRivers.map((river, index) => (
        <Polyline
          key={index}
          positions={river.coordinates}
          color="#3b82f6"
          weight={3}
          opacity={0.7}
        >
          <Popup>
            <div className="text-center">
              <h4 className="font-semibold text-blue-800">{river.name}</h4>
              <p className="text-sm text-gray-600">Major river system</p>
            </div>
          </Popup>
        </Polyline>
      ))}
      
      {/* Hydropower Projects */}
      {hydropowerProjects.map((project, index) => (
        <Marker
          key={index}
          position={project.position}
          icon={project.icon}
        >
          <Popup>
            <div className="min-w-[200px]">
              <h3 className="font-bold text-lg text-slate-800 mb-2">{project.name}</h3>
              <div className="space-y-1 text-sm">
                <p><span className="font-semibold">Capacity:</span> {project.capacity}</p>
                <p><span className="font-semibold">Status:</span> 
                  <span className={`ml-1 px-2 py-1 rounded text-xs font-medium ${
                    project.status === 'Operational' ? 'bg-green-100 text-green-800' :
                    project.status === 'Under Construction' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {project.status}
                  </span>
                </p>
                <p><span className="font-semibold">River:</span> {project.river}</p>
              </div>
            </div>
          </Popup>
        </Marker>
      ))}
    </MapContainer>
  );
}
