'use client';

import { motion } from 'framer-motion';
import { 
  SparklesIcon as LeafIcon, 
  BoltIcon, 
  CurrencyDollarIcon, 
  HomeIcon,
  ExclamationTriangleIcon,
  GlobeAltIcon,
  UsersIcon,
  ShieldExclamationIcon
} from '@heroicons/react/24/outline';

interface ProConItem {
  icon: React.ComponentType<{ className?: string }>;
  title: string;
  description: string;
}

export default function ProsCons() {
  const pros: ProConItem[] = [
    {
      icon: LeafIcon,
      title: "Clean & Renewable Energy",
      description: "Produces electricity without harmful emissions, helping combat climate change and reducing Nepal's carbon footprint."
    },
    {
      icon: BoltIcon,
      title: "Energy Independence",
      description: "Reduces reliance on imported fossil fuels and provides a stable, domestic source of electricity for the nation."
    },
    {
      icon: CurrencyDollarIcon,
      title: "Revenue from Electricity Exports",
      description: "Surplus power can be exported to neighboring countries, generating significant foreign exchange earnings."
    },
    {
      icon: HomeIcon,
      title: "Rural Electrification & Development",
      description: "Brings electricity to remote communities, enabling economic development, education, and improved quality of life."
    }
  ];

  const cons: ProConItem[] = [
    {
      icon: CurrencyDollarIcon,
      title: "High Upfront Costs",
      description: "Hydropower projects require massive initial investments, often requiring international financing and long payback periods."
    },
    {
      icon: GlobeAltIcon,
      title: "Environmental Impact",
      description: "Dams can disrupt river ecosystems, affect fish migration, and alter downstream water flow patterns."
    },
    {
      icon: UsersIcon,
      title: "Social Displacement",
      description: "Large projects may require relocating communities, affecting traditional livelihoods and cultural heritage sites."
    },
    {
      icon: ShieldExclamationIcon,
      title: "Geological Risks",
      description: "Nepal's seismic activity and monsoon-triggered landslides pose ongoing risks to hydropower infrastructure."
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  return (
    <section className="min-h-screen bg-white py-20">
      <div className="max-w-7xl mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-light text-slate-800 mb-6">
            The Complete Picture
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            Understanding both the tremendous opportunities and significant challenges 
            of hydropower development in Nepal
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Benefits/Pros */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-8 shadow-lg border border-green-100"
          >
            <div className="flex items-center mb-8">
              <div className="bg-green-500 rounded-full p-3 mr-4">
                <LeafIcon className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-3xl font-semibold text-green-800">
                The Promise
              </h3>
            </div>

            <div className="space-y-6">
              {pros.map((pro, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="bg-white rounded-lg p-6 shadow-sm border border-green-100 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start">
                    <div className="bg-green-100 rounded-lg p-3 mr-4 flex-shrink-0">
                      <pro.icon className="w-6 h-6 text-green-600" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-slate-800 mb-2">
                        {pro.title}
                      </h4>
                      <p className="text-slate-600 leading-relaxed">
                        {pro.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Challenges/Cons */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            className="bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl p-8 shadow-lg border border-orange-100"
          >
            <div className="flex items-center mb-8">
              <div className="bg-orange-500 rounded-full p-3 mr-4">
                <ExclamationTriangleIcon className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-3xl font-semibold text-orange-800">
                The Challenges
              </h3>
            </div>

            <div className="space-y-6">
              {cons.map((con, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="bg-white rounded-lg p-6 shadow-sm border border-orange-100 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start">
                    <div className="bg-orange-100 rounded-lg p-3 mr-4 flex-shrink-0">
                      <con.icon className="w-6 h-6 text-orange-600" />
                    </div>
                    <div>
                      <h4 className="text-lg font-semibold text-slate-800 mb-2">
                        {con.title}
                      </h4>
                      <p className="text-slate-600 leading-relaxed">
                        {con.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>

        {/* Conclusion */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="mt-16 bg-slate-100 rounded-2xl p-8 shadow-lg text-center"
        >
          <h3 className="text-2xl font-semibold text-slate-800 mb-6">
            The Path Forward
          </h3>
          <div className="max-w-4xl mx-auto">
            <p className="text-lg text-slate-700 leading-relaxed mb-6">
              Nepal stands at a crossroads with its hydropower development. The potential for transformation 
              is immense, but success depends on sustainable and responsible development practices that 
              balance economic benefits with environmental protection and social equity.
            </p>
            <div className="grid md:grid-cols-3 gap-6 mt-8">
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h4 className="font-semibold text-slate-800 mb-2">Sustainable Planning</h4>
                <p className="text-sm text-slate-600">
                  Comprehensive environmental and social impact assessments for all projects
                </p>
              </div>
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h4 className="font-semibold text-slate-800 mb-2">Community Engagement</h4>
                <p className="text-sm text-slate-600">
                  Meaningful consultation and benefit-sharing with affected communities
                </p>
              </div>
              <div className="bg-white rounded-lg p-6 shadow-sm">
                <h4 className="font-semibold text-slate-800 mb-2">Technology Innovation</h4>
                <p className="text-sm text-slate-600">
                  Adopting latest technologies to minimize environmental impact and maximize efficiency
                </p>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
