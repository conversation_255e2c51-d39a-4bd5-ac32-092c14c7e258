'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';

export default function HowItWorks() {
  const [currentStep, setCurrentStep] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  const steps = [
    {
      title: "Water Storage",
      description: "Water is stored in a reservoir, creating potential energy from height.",
      highlight: "reservoir"
    },
    {
      title: "Gravity Flow", 
      description: "Gravity pulls the water down through the penstock, gaining speed.",
      highlight: "penstock"
    },
    {
      title: "Turbine Rotation",
      description: "The force of flowing water spins the turbine rapidly.",
      highlight: "turbine"
    },
    {
      title: "Electricity Generation",
      description: "The spinning turbine powers the generator, creating electricity.",
      highlight: "generator"
    },
    {
      title: "Power Distribution",
      description: "Electricity flows through power lines to homes and businesses.",
      highlight: "powerlines"
    }
  ];

  const startAnimation = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentStep(0);
    
    const timer = setInterval(() => {
      setCurrentStep(prev => {
        if (prev >= steps.length - 1) {
          clearInterval(timer);
          setTimeout(() => setIsAnimating(false), 1000);
          return prev;
        }
        return prev + 1;
      });
    }, 2000);
  };

  return (
    <section className="min-h-screen bg-slate-50 py-20">
      <div className="max-w-7xl mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-light text-slate-800 mb-6">
            How Hydropower Works
          </h2>
          <p className="text-xl text-slate-600 max-w-2xl mx-auto">
            Discover the simple yet powerful process of generating electricity from flowing water
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Interactive Diagram */}
          <div className="relative">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
              className="bg-white rounded-2xl shadow-lg p-8"
            >
              <div className="relative w-full h-96">
                {/* SVG Hydropower Diagram */}
                <svg viewBox="0 0 400 300" className="w-full h-full">
                  {/* Reservoir */}
                  <motion.rect
                    x="20" y="50" width="120" height="80"
                    fill={currentStep >= 0 ? "#3b82f6" : "#e2e8f0"}
                    rx="8"
                    animate={currentStep >= 0 ? { scale: [1, 1.05, 1] } : {}}
                    transition={{ duration: 1 }}
                  />
                  <text x="80" y="95" textAnchor="middle" className="fill-white text-sm font-medium">
                    Reservoir
                  </text>

                  {/* Dam */}
                  <rect x="140" y="80" width="20" height="50" fill="#6b7280" />
                  
                  {/* Penstock */}
                  <motion.line
                    x1="160" y1="130" x2="220" y2="180"
                    stroke={currentStep >= 1 ? "#3b82f6" : "#e2e8f0"}
                    strokeWidth="12"
                    strokeLinecap="round"
                  />
                  
                  {/* Water Flow Animation */}
                  {currentStep >= 1 && (
                    <motion.circle
                      r="4"
                      fill="#60a5fa"
                      initial={{ x: 160, y: 130 }}
                      animate={{ x: 220, y: 180 }}
                      transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                    />
                  )}

                  {/* Turbine */}
                  <motion.circle
                    cx="240" cy="200" r="25"
                    fill={currentStep >= 2 ? "#10b981" : "#e2e8f0"}
                    animate={currentStep >= 2 ? { rotate: 360 } : {}}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    style={{ originX: "240px", originY: "200px" }}
                  />
                  <text x="240" y="240" textAnchor="middle" className="fill-slate-600 text-xs">
                    Turbine
                  </text>

                  {/* Generator */}
                  <motion.rect
                    x="280" y="185" width="40" height="30"
                    fill={currentStep >= 3 ? "#f59e0b" : "#e2e8f0"}
                    rx="4"
                    animate={currentStep >= 3 ? { scale: [1, 1.1, 1] } : {}}
                    transition={{ duration: 0.5, repeat: Infinity }}
                  />
                  <text x="300" y="240" textAnchor="middle" className="fill-slate-600 text-xs">
                    Generator
                  </text>

                  {/* Power Lines */}
                  <motion.path
                    d="M320 200 L380 200 L380 50"
                    stroke={currentStep >= 4 ? "#eab308" : "#e2e8f0"}
                    strokeWidth="3"
                    fill="none"
                  />
                  
                  {/* Electricity Animation */}
                  {currentStep >= 4 && (
                    <>
                      <motion.circle
                        r="3"
                        fill="#fbbf24"
                        initial={{ x: 320, y: 200 }}
                        animate={{ x: 380, y: 50 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "easeInOut" }}
                      />
                      <motion.circle
                        r="2"
                        fill="#f59e0b"
                        initial={{ x: 320, y: 200 }}
                        animate={{ x: 380, y: 50 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "easeInOut", delay: 0.3 }}
                      />
                    </>
                  )}

                  {/* House */}
                  <rect x="360" y="30" width="30" height="20" fill="#64748b" />
                  <polygon points="360,30 375,15 390,30" fill="#475569" />
                  
                  {/* Light bulb in house */}
                  <motion.circle
                    cx="375" cy="40" r="3"
                    fill={currentStep >= 4 ? "#fbbf24" : "#e2e8f0"}
                    animate={currentStep >= 4 ? { opacity: [0.5, 1, 0.5] } : {}}
                    transition={{ duration: 1, repeat: Infinity }}
                  />
                </svg>
              </div>

              <div className="mt-6 text-center">
                <button
                  onClick={startAnimation}
                  disabled={isAnimating}
                  className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-8 py-3 rounded-lg font-medium transition-colors"
                >
                  {isAnimating ? 'Watch the Process...' : 'Start Animation'}
                </button>
              </div>
            </motion.div>
          </div>

          {/* Steps Description */}
          <div className="space-y-6">
            {steps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className={`p-6 rounded-lg border-l-4 transition-all duration-300 ${
                  currentStep >= index 
                    ? 'bg-blue-50 border-blue-500 shadow-md' 
                    : 'bg-white border-gray-200'
                }`}
              >
                <div className="flex items-center mb-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold mr-3 ${
                    currentStep >= index ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-500'
                  }`}>
                    {index + 1}
                  </div>
                  <h3 className="text-lg font-semibold text-slate-800">
                    {step.title}
                  </h3>
                </div>
                <p className="text-slate-600 ml-11">
                  {step.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
