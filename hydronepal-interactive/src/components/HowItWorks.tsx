'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import LoadingSpinner from './LoadingSpinner';

export default function HowItWorks() {
  const [currentStep, setCurrentStep] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  const steps = [
    {
      title: "Water Storage",
      description: "Water is stored in a reservoir, creating potential energy from height.",
      highlight: "reservoir"
    },
    {
      title: "Gravity Flow", 
      description: "Gravity pulls the water down through the penstock, gaining speed.",
      highlight: "penstock"
    },
    {
      title: "Turbine Rotation",
      description: "The force of flowing water spins the turbine rapidly.",
      highlight: "turbine"
    },
    {
      title: "Electricity Generation",
      description: "The spinning turbine powers the generator, creating electricity.",
      highlight: "generator"
    },
    {
      title: "Power Distribution",
      description: "Electricity flows through power lines to homes and businesses.",
      highlight: "powerlines"
    }
  ];

  const startAnimation = () => {
    if (isAnimating) return;
    setIsAnimating(true);
    setCurrentStep(0);
    
    const timer = setInterval(() => {
      setCurrentStep(prev => {
        if (prev >= steps.length - 1) {
          clearInterval(timer);
          setTimeout(() => setIsAnimating(false), 1000);
          return prev;
        }
        return prev + 1;
      });
    }, 2000);
  };

  return (
    <section className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-slate-50 py-24">
      <div className="max-w-7xl mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-medium mb-6">
            <span className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></span>
            Interactive Learning
          </div>
          <h2 className="text-5xl md:text-6xl font-bold text-slate-900 mb-8 leading-tight">
            How Hydropower
            <span className="text-gradient bg-gradient-to-r from-blue-600 to-emerald-600 bg-clip-text text-transparent"> Works</span>
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Discover the elegant process of transforming flowing water into clean electricity through our interactive visualization
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Interactive Diagram */}
          <div className="relative">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8 }}
              className="bg-white rounded-3xl shadow-2xl p-10 border border-slate-200/50"
            >
              <div className="text-center mb-8">
                <h3 className="text-2xl font-semibold text-slate-800 mb-3">Interactive Diagram</h3>
                <p className="text-slate-600">Watch the process unfold step by step</p>
              </div>
              <div className="relative w-full h-96 bg-gradient-to-b from-sky-50 to-blue-50 rounded-2xl p-6">
                {/* SVG Hydropower Diagram */}
                <svg viewBox="0 0 400 300" className="w-full h-full">
                  {/* Reservoir */}
                  <motion.rect
                    x="20" y="50" width="120" height="80"
                    fill={currentStep >= 0 ? "#3b82f6" : "#e2e8f0"}
                    rx="8"
                    animate={currentStep >= 0 ? { scale: [1, 1.05, 1] } : {}}
                    transition={{ duration: 1 }}
                  />
                  <text x="80" y="95" textAnchor="middle" className="fill-white text-sm font-medium">
                    Reservoir
                  </text>

                  {/* Dam */}
                  <rect x="140" y="80" width="20" height="50" fill="#6b7280" />
                  
                  {/* Penstock */}
                  <motion.line
                    x1="160" y1="130" x2="220" y2="180"
                    stroke={currentStep >= 1 ? "#3b82f6" : "#e2e8f0"}
                    strokeWidth="12"
                    strokeLinecap="round"
                  />
                  
                  {/* Water Flow Animation */}
                  {currentStep >= 1 && (
                    <motion.circle
                      r="4"
                      fill="#60a5fa"
                      initial={{ x: 160, y: 130 }}
                      animate={{ x: 220, y: 180 }}
                      transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                    />
                  )}

                  {/* Turbine */}
                  <motion.circle
                    cx="240" cy="200" r="25"
                    fill={currentStep >= 2 ? "#10b981" : "#e2e8f0"}
                    animate={currentStep >= 2 ? { rotate: 360 } : {}}
                    transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                    style={{ originX: "240px", originY: "200px" }}
                  />
                  <text x="240" y="240" textAnchor="middle" className="fill-slate-600 text-xs">
                    Turbine
                  </text>

                  {/* Generator */}
                  <motion.rect
                    x="280" y="185" width="40" height="30"
                    fill={currentStep >= 3 ? "#f59e0b" : "#e2e8f0"}
                    rx="4"
                    animate={currentStep >= 3 ? { scale: [1, 1.1, 1] } : {}}
                    transition={{ duration: 0.5, repeat: Infinity }}
                  />
                  <text x="300" y="240" textAnchor="middle" className="fill-slate-600 text-xs">
                    Generator
                  </text>

                  {/* Power Lines */}
                  <motion.path
                    d="M320 200 L380 200 L380 50"
                    stroke={currentStep >= 4 ? "#eab308" : "#e2e8f0"}
                    strokeWidth="3"
                    fill="none"
                  />
                  
                  {/* Electricity Animation */}
                  {currentStep >= 4 && (
                    <>
                      <motion.circle
                        r="3"
                        fill="#fbbf24"
                        initial={{ x: 320, y: 200 }}
                        animate={{ x: 380, y: 50 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "easeInOut" }}
                      />
                      <motion.circle
                        r="2"
                        fill="#f59e0b"
                        initial={{ x: 320, y: 200 }}
                        animate={{ x: 380, y: 50 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "easeInOut", delay: 0.3 }}
                      />
                    </>
                  )}

                  {/* House */}
                  <rect x="360" y="30" width="30" height="20" fill="#64748b" />
                  <polygon points="360,30 375,15 390,30" fill="#475569" />
                  
                  {/* Light bulb in house */}
                  <motion.circle
                    cx="375" cy="40" r="3"
                    fill={currentStep >= 4 ? "#fbbf24" : "#e2e8f0"}
                    animate={currentStep >= 4 ? { opacity: [0.5, 1, 0.5] } : {}}
                    transition={{ duration: 1, repeat: Infinity }}
                  />
                </svg>
              </div>

              <div className="mt-8 text-center">
                <motion.button
                  onClick={startAnimation}
                  disabled={isAnimating}
                  className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 disabled:from-blue-400 disabled:to-blue-500 text-white px-10 py-4 rounded-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none transition-all duration-300"
                  whileHover={{ scale: isAnimating ? 1 : 1.05 }}
                  whileTap={{ scale: isAnimating ? 1 : 0.95 }}
                >
                  {isAnimating ? (
                    <span className="flex items-center gap-3">
                      <LoadingSpinner size="sm" color="white" />
                      Watch the Process...
                    </span>
                  ) : (
                    'Start Animation'
                  )}
                </motion.button>
              </div>
            </motion.div>
          </div>

          {/* Enhanced Steps Description */}
          <div className="space-y-4">
            <div className="mb-8">
              <h3 className="text-2xl font-semibold text-slate-800 mb-3">The Process</h3>
              <p className="text-slate-600">Follow each step as water transforms into electricity</p>
            </div>
            {steps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: 30 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className={`group relative p-6 rounded-2xl border transition-all duration-500 hover:shadow-lg ${
                  currentStep >= index
                    ? 'bg-gradient-to-r from-blue-50 to-blue-100/50 border-blue-200 shadow-md'
                    : 'bg-white border-slate-200 hover:border-slate-300'
                }`}
              >
                <div className="flex items-start gap-4">
                  <div className={`relative flex-shrink-0 w-12 h-12 rounded-xl flex items-center justify-center text-lg font-bold transition-all duration-300 ${
                    currentStep >= index
                      ? 'bg-blue-500 text-white shadow-lg'
                      : 'bg-slate-100 text-slate-500 group-hover:bg-slate-200'
                  }`}>
                    {index + 1}
                    {currentStep >= index && (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="absolute -inset-1 bg-blue-500/20 rounded-xl blur-sm"
                      />
                    )}
                  </div>
                  <div className="flex-1">
                    <h4 className="text-xl font-semibold text-slate-800 mb-2 group-hover:text-blue-700 transition-colors">
                      {step.title}
                    </h4>
                    <p className="text-slate-600 leading-relaxed">
                      {step.description}
                    </p>
                  </div>
                </div>

                {/* Progress indicator */}
                {currentStep >= index && (
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: '100%' }}
                    className="absolute bottom-0 left-0 h-1 bg-gradient-to-r from-blue-500 to-blue-600 rounded-b-2xl"
                    transition={{ duration: 0.5, delay: 0.2 }}
                  />
                )}
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
