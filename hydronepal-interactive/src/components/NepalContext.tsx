'use client';

import { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import dynamic from 'next/dynamic';
import * as d3 from 'd3';

// Dynamically import map component to avoid SSR issues
const NepalMap = dynamic(() => import('./NepalMap'), { ssr: false });

interface HydropowerData {
  theoretical: number;
  feasible: number;
  installed: number;
}

export default function NepalContext() {
  const chartRef = useRef<SVGSVGElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  const data: HydropowerData = {
    theoretical: 83000,
    feasible: 42000,
    installed: 2500
  };

  const chartData = [
    { label: 'Theoretical Potential', value: data.theoretical, color: '#3b82f6' },
    { label: 'Technically Feasible', value: data.feasible, color: '#10b981' },
    { label: 'Currently Installed', value: data.installed, color: '#f59e0b' }
  ];

  useEffect(() => {
    if (!isVisible || !chartRef.current) return;

    const svg = d3.select(chartRef.current);
    svg.selectAll('*').remove();

    const margin = { top: 20, right: 30, bottom: 80, left: 80 };
    const width = 500 - margin.left - margin.right;
    const height = 400 - margin.top - margin.bottom;

    const g = svg
      .attr('width', width + margin.left + margin.right)
      .attr('height', height + margin.top + margin.bottom)
      .append('g')
      .attr('transform', `translate(${margin.left},${margin.top})`);

    // Scales
    const xScale = d3
      .scaleBand()
      .domain(chartData.map(d => d.label))
      .range([0, width])
      .padding(0.2);

    const yScale = d3
      .scaleLinear()
      .domain([0, d3.max(chartData, d => d.value) || 0])
      .range([height, 0]);

    // Axes
    g.append('g')
      .attr('transform', `translate(0,${height})`)
      .call(d3.axisBottom(xScale))
      .selectAll('text')
      .style('text-anchor', 'middle')
      .style('font-size', '12px')
      .call(wrap, xScale.bandwidth());

    g.append('g')
      .call(d3.axisLeft(yScale).tickFormat(d => `${d3.format('.0s')(d)}W`))
      .selectAll('text')
      .style('font-size', '12px');

    // Bars
    const bars = g
      .selectAll('.bar')
      .data(chartData)
      .enter()
      .append('rect')
      .attr('class', 'bar')
      .attr('x', d => xScale(d.label) || 0)
      .attr('width', xScale.bandwidth())
      .attr('y', height)
      .attr('height', 0)
      .attr('fill', d => d.color)
      .attr('rx', 4);

    // Animate bars
    bars
      .transition()
      .duration(1500)
      .delay((_, i) => i * 200)
      .attr('y', d => yScale(d.value))
      .attr('height', d => height - yScale(d.value));

    // Value labels
    g.selectAll('.label')
      .data(chartData)
      .enter()
      .append('text')
      .attr('class', 'label')
      .attr('x', d => (xScale(d.label) || 0) + xScale.bandwidth() / 2)
      .attr('y', height)
      .attr('text-anchor', 'middle')
      .style('font-size', '12px')
      .style('font-weight', 'bold')
      .style('fill', '#374151')
      .text(d => `${d3.format('.1s')(d.value)}W`)
      .transition()
      .duration(1500)
      .delay((_, i) => i * 200)
      .attr('y', d => yScale(d.value) - 10);

    // Y-axis label
    g.append('text')
      .attr('transform', 'rotate(-90)')
      .attr('y', 0 - margin.left)
      .attr('x', 0 - (height / 2))
      .attr('dy', '1em')
      .style('text-anchor', 'middle')
      .style('font-size', '14px')
      .style('font-weight', 'bold')
      .style('fill', '#374151')
      .text('Capacity (Megawatts)');

    // Title
    g.append('text')
      .attr('x', width / 2)
      .attr('y', 0 - (margin.top / 2))
      .attr('text-anchor', 'middle')
      .style('font-size', '16px')
      .style('font-weight', 'bold')
      .style('fill', '#1f2937')
      .text("Nepal's Hydropower Potential");

  }, [isVisible, chartData]);

  // Text wrapping function for x-axis labels
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  function wrap(text: any, width: number) {
    text.each(function(this: SVGTextElement) {
      const textElement = d3.select(this);
      const words = textElement.text().split(/\s+/).reverse();
      let word;
      let line: string[] = [];
      let lineNumber = 0;
      const lineHeight = 1.1;
      const y = textElement.attr('y');
      const dy = parseFloat(textElement.attr('dy'));
      let tspan = textElement.text(null).append('tspan').attr('x', 0).attr('y', y).attr('dy', dy + 'em');
      
      while ((word = words.pop())) {
        line.push(word);
        tspan.text(line.join(' '));
        if ((tspan.node()?.getComputedTextLength() || 0) > width) {
          line.pop();
          tspan.text(line.join(' '));
          line = [word];
          tspan = textElement.append('tspan').attr('x', 0).attr('y', y).attr('dy', ++lineNumber * lineHeight + dy + 'em').text(word);
        }
      }
    });
  }

  return (
    <section className="min-h-screen bg-white py-20">
      <div className="max-w-7xl mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-light text-slate-800 mb-6">
            Hydropower in Nepal
          </h2>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            Nepal&apos;s unique geography makes it one of the world&apos;s richest countries in hydropower potential, 
            with vast untapped resources flowing from the Himalayas
          </p>
        </motion.div>

        <div className="grid lg:grid-cols-2 gap-16 items-start">
          {/* Interactive Map */}
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="bg-slate-50 rounded-2xl p-6 shadow-lg"
          >
            <h3 className="text-2xl font-semibold text-slate-800 mb-6 text-center">
              Major Hydropower Projects
            </h3>
            <div className="h-96 rounded-lg overflow-hidden">
              <NepalMap />
            </div>
            <div className="mt-4 text-sm text-slate-600 text-center">
              Click on markers to learn about specific projects
            </div>
          </motion.div>

          {/* Data Visualization */}
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            onViewportEnter={() => setIsVisible(true)}
            transition={{ duration: 0.8 }}
            className="bg-slate-50 rounded-2xl p-6 shadow-lg"
          >
            <div className="text-center mb-6">
              <h3 className="text-2xl font-semibold text-slate-800 mb-2">
                The Untapped Potential
              </h3>
              <p className="text-slate-600">
                Nepal has utilized less than 3% of its hydropower potential
              </p>
            </div>
            
            <div className="flex justify-center">
              <svg ref={chartRef} className="w-full max-w-lg"></svg>
            </div>

            <div className="mt-8 space-y-4">
              <div className="bg-white p-4 rounded-lg">
                <h4 className="font-semibold text-slate-800 mb-2">Why Nepal is Ideal for Hydropower:</h4>
                <ul className="text-slate-600 space-y-2">
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">•</span>
                    High altitude differences create massive potential energy
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">•</span>
                    Abundant water from Himalayan glaciers and monsoons
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">•</span>
                    Year-round water flow from perennial rivers
                  </li>
                  <li className="flex items-start">
                    <span className="text-blue-500 mr-2">•</span>
                    Strategic location between energy-hungry markets
                  </li>
                </ul>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg border-l-4 border-blue-500">
                <p className="text-slate-700 text-sm">
                  <strong>Did you know?</strong> If fully developed, Nepal&apos;s hydropower could meet 
                  its domestic energy needs and export significant amounts to neighboring countries, 
                  transforming the nation&apos;s economy.
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
