'use client';

import { motion } from 'framer-motion';
import { ChevronDownIcon, PlayIcon, SparklesIcon } from '@heroicons/react/24/outline';

export default function Hero() {
  const scrollToNext = () => {
    const nextSection = document.querySelector('#how-it-works');
    if (nextSection) {
      nextSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <section className="relative h-screen flex items-center justify-center overflow-hidden">
      {/* Enhanced Background */}
      <div className="absolute inset-0">
        {/* Primary gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-900 via-indigo-900 to-emerald-900"></div>

        {/* Animated geometric patterns */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-400 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute top-3/4 right-1/4 w-80 h-80 bg-emerald-400 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }}></div>
          <div className="absolute bottom-1/4 left-1/3 w-48 h-48 bg-cyan-400 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
          <div className="absolute top-1/2 right-1/3 w-32 h-32 bg-purple-400 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '3s' }}></div>
        </div>

        {/* Overlay with subtle texture */}
        <div className="absolute inset-0 bg-black/40"></div>

        {/* Animated water ripple effect */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-blue-500/30 to-transparent animate-pulse"></div>
        </div>
      </div>

      {/* Floating particles */}
      <div className="absolute inset-0 overflow-hidden">
        {[
          { left: 20, top: 15, duration: 5, delay: 0 },
          { left: 80, top: 25, duration: 4.5, delay: 0.5 },
          { left: 60, top: 70, duration: 5.5, delay: 1 },
          { left: 30, top: 80, duration: 4, delay: 1.5 },
          { left: 90, top: 40, duration: 6, delay: 0.8 },
          { left: 15, top: 50, duration: 4.8, delay: 1.2 },
        ].map((particle, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-white/20 rounded-full"
            style={{
              left: `${particle.left}%`,
              top: `${particle.top}%`,
            }}
            animate={{
              y: [0, -100, 0],
              opacity: [0, 1, 0],
            }}
            transition={{
              duration: particle.duration,
              repeat: Infinity,
              delay: particle.delay,
            }}
          />
        ))}
      </div>

      {/* Content */}
      <div className="relative z-10 text-center text-white px-6 max-w-6xl">
        {/* Badge */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.1 }}
          className="inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-md rounded-full border border-white/20 mb-8"
        >
          <SparklesIcon className="w-4 h-4 text-yellow-300" />
          <span className="text-sm font-medium text-white/90">Interactive Learning Experience</span>
        </motion.div>

        <motion.h1
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1.2, delay: 0.3 }}
          className="text-6xl md:text-8xl font-bold mb-6 tracking-tight"
        >
          <span className="text-gradient bg-gradient-to-r from-blue-200 via-white to-emerald-200 bg-clip-text text-transparent">
            Nepal&apos;s
          </span>
          <br />
          <span className="text-white">Hydropower</span>
        </motion.h1>

        <motion.h2
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 0.8 }}
          className="text-2xl md:text-3xl font-light mb-8 text-blue-100 max-w-3xl mx-auto"
        >
          Harnessing the Power of the Himalayas
        </motion.h2>

        <motion.p
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 1.2 }}
          className="text-lg md:text-xl font-light text-blue-200/90 mb-12 max-w-2xl mx-auto leading-relaxed"
        >
          Discover how Nepal&apos;s mighty rivers can transform into clean, renewable energy through an immersive, educational journey
        </motion.p>

        {/* Call to Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 1.6 }}
          className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16"
        >
          <button
            onClick={scrollToNext}
            className="group w-full sm:w-auto px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 rounded-full font-semibold text-white shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 flex items-center justify-center gap-2 touch-manipulation"
          >
            Start Exploring
            <ChevronDownIcon className="w-5 h-5 group-hover:translate-y-1 transition-transform" />
          </button>

          <button className="group w-full sm:w-auto px-8 py-4 bg-white/10 backdrop-blur-md hover:bg-white/20 rounded-full font-semibold text-white border border-white/30 hover:border-white/50 transition-all duration-300 flex items-center justify-center gap-2 touch-manipulation">
            <PlayIcon className="w-5 h-5" />
            Watch Demo
          </button>
        </motion.div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, delay: 2 }}
          className="grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8 max-w-4xl mx-auto mb-16"
        >
          <div className="text-center p-4 bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10">
            <div className="text-2xl sm:text-3xl font-bold text-white mb-2">83,000 MW</div>
            <div className="text-sm text-blue-200">Theoretical Potential</div>
          </div>
          <div className="text-center p-4 bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10">
            <div className="text-2xl sm:text-3xl font-bold text-white mb-2">42,000 MW</div>
            <div className="text-sm text-blue-200">Economically Feasible</div>
          </div>
          <div className="text-center p-4 bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10">
            <div className="text-2xl sm:text-3xl font-bold text-white mb-2">2,000 MW</div>
            <div className="text-sm text-blue-200">Currently Developed</div>
          </div>
        </motion.div>

        {/* Enhanced Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 2.5 }}
          className="flex flex-col items-center cursor-pointer group"
          onClick={scrollToNext}
        >
          <span className="text-sm font-medium mb-3 text-blue-200/80 group-hover:text-white transition-colors">
            Discover How It Works
          </span>
          <motion.div
            animate={{ y: [0, 8, 0] }}
            transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            className="p-2 rounded-full border border-white/30 group-hover:border-white/60 group-hover:bg-white/10 transition-all duration-300"
          >
            <ChevronDownIcon className="w-6 h-6 text-blue-300 group-hover:text-white transition-colors" />
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
