import type { Metadata } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "HydroNepal Interactive Guide - Understanding Nepal's Hydropower Potential",
  description: "An interactive journey into Nepal's hydropower sector. Learn how hydropower works, explore Nepal's vast potential, and understand the opportunities and challenges in harnessing the Himalayan rivers.",
  keywords: "Nepal, hydropower, renewable energy, Himalayas, electricity, sustainable development",
  authors: [{ name: "HydroNepal Team" }],
  openGraph: {
    title: "HydroNepal Interactive Guide",
    description: "Discover Nepal's hydropower potential through interactive visualizations and educational content",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
