import Navigation from '@/components/Navigation';
import Hero from '@/components/Hero';
import HowItWorks from '@/components/HowItWorks';
import NepalContext from '@/components/NepalContext';
import Glossary from '@/components/Glossary';
import ProsCons from '@/components/ProsCons';

export default function Home() {
  return (
    <main className="relative">
      {/* Navigation */}
      <Navigation />

      {/* Hero Section */}
      <section id="home">
        <Hero />
      </section>

      {/* How It Works Section */}
      <section id="how-it-works">
        <HowItWorks />
      </section>

      {/* Nepal Context Section */}
      <section id="nepal-context">
        <NepalContext />
      </section>

      {/* Glossary Section */}
      <section id="glossary">
        <Glossary />
      </section>

      {/* Pros and Cons Section */}
      <section id="pros-cons">
        <ProsCons />
      </section>

      {/* Enhanced Footer */}
      <footer className="bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white py-16">
        <div className="max-w-6xl mx-auto px-6">
          <div className="grid md:grid-cols-3 gap-12 mb-12">
            {/* Brand Section */}
            <div className="md:col-span-1">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center">
                  <span className="text-white font-bold text-lg">H</span>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-white">HydroNepal</h3>
                  <p className="text-sm text-slate-400">Interactive Guide</p>
                </div>
              </div>
              <p className="text-slate-300 leading-relaxed">
                Understanding Nepal&apos;s hydropower potential through interactive visualizations and educational content.
              </p>
            </div>

            {/* Quick Links */}
            <div className="md:col-span-1">
              <h4 className="text-lg font-semibold text-white mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li><a href="#home" className="text-slate-300 hover:text-blue-400 transition-colors">Home</a></li>
                <li><a href="#how-it-works" className="text-slate-300 hover:text-blue-400 transition-colors">How It Works</a></li>
                <li><a href="#nepal-context" className="text-slate-300 hover:text-blue-400 transition-colors">Nepal Context</a></li>
                <li><a href="#glossary" className="text-slate-300 hover:text-blue-400 transition-colors">Glossary</a></li>
                <li><a href="#pros-cons" className="text-slate-300 hover:text-blue-400 transition-colors">Pros & Cons</a></li>
              </ul>
            </div>

            {/* About */}
            <div className="md:col-span-1">
              <h4 className="text-lg font-semibold text-white mb-4">About This Project</h4>
              <p className="text-slate-300 text-sm leading-relaxed mb-4">
                An educational resource designed for students, journalists, policymakers, and anyone curious about renewable energy.
              </p>
              <div className="flex flex-wrap gap-2">
                <span className="px-3 py-1 bg-slate-700 text-slate-300 text-xs rounded-full">Next.js</span>
                <span className="px-3 py-1 bg-slate-700 text-slate-300 text-xs rounded-full">D3.js</span>
                <span className="px-3 py-1 bg-slate-700 text-slate-300 text-xs rounded-full">Leaflet</span>
                <span className="px-3 py-1 bg-slate-700 text-slate-300 text-xs rounded-full">TypeScript</span>
              </div>
            </div>
          </div>

          {/* Bottom Section */}
          <div className="border-t border-slate-700 pt-8 text-center">
            <p className="text-slate-400 text-sm">
              © 2024 HydroNepal Interactive Guide. Built with passion for sustainable energy education.
            </p>
          </div>
        </div>
      </footer>
    </main>
  );
}
