import Navigation from '@/components/Navigation';
import Hero from '@/components/Hero';
import HowItWorks from '@/components/HowItWorks';
import NepalContext from '@/components/NepalContext';
import Glossary from '@/components/Glossary';
import ProsCons from '@/components/ProsCons';

export default function Home() {
  return (
    <main className="relative">
      {/* Navigation */}
      <Navigation />
      
      {/* Hero Section */}
      <section id="home">
        <Hero />
      </section>
      
      {/* How It Works Section */}
      <section id="how-it-works">
        <HowItWorks />
      </section>
      
      {/* Nepal Context Section */}
      <section id="nepal-context">
        <NepalContext />
      </section>
      
      {/* Glossary Section */}
      <section id="glossary">
        <Glossary />
      </section>
      
      {/* Pros and Cons Section */}
      <section id="pros-cons">
        <ProsCons />
      </section>
      
      {/* Footer */}
      <footer className="bg-slate-800 text-white py-12">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h3 className="text-2xl font-light mb-4">The HydroNepal Interactive Guide</h3>
          <p className="text-slate-300 mb-6">
            Understanding Nepal&apos;s hydropower potential and its role in sustainable development
          </p>
          <div className="text-sm text-slate-400">
            <p>An educational resource for students, journalists, policymakers, and curious minds</p>
            <p className="mt-2">Built with Next.js, D3.js, and Leaflet</p>
          </div>
        </div>
      </footer>
    </main>
  );
}
